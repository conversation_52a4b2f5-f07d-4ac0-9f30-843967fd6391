<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Select Location Dropdown Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
    <style>
        /* Multi-Select Dropdown Styles */
        .multi-select-container {
            position: relative;
            width: 100%;
        }

        .multi-select-dropdown {
            position: relative;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            background-color: #fff;
            transition: all 0.3s ease;
        }

        .multi-select-dropdown:hover {
            border-color: #86b7fe;
        }

        .multi-select-dropdown.active {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .multi-select-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.375rem 0.75rem;
            cursor: pointer;
            min-height: 38px;
        }

        .selected-options {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
            flex: 1;
            align-items: center;
        }

        .selected-options .placeholder {
            color: #6c757d;
            font-size: 1rem;
        }

        .selected-tag {
            display: inline-flex;
            align-items: center;
            background-color: #0d6efd;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
            gap: 0.25rem;
            animation: fadeIn 0.3s ease;
        }

        .selected-tag .remove-tag {
            cursor: pointer;
            font-size: 0.75rem;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .selected-tag .remove-tag:hover {
            opacity: 1;
        }

        .dropdown-arrow {
            transition: transform 0.3s ease;
            color: #6c757d;
        }

        .multi-select-dropdown.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .multi-select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ced4da;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .option-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            gap: 0.5rem;
        }

        .option-item:hover {
            background-color: #f8f9fa;
        }

        .option-item input[type="checkbox"] {
            margin: 0;
            cursor: pointer;
        }

        .option-item label {
            margin: 0;
            cursor: pointer;
            flex: 1;
            font-weight: 500;
        }

        /* Dynamic Location Fields Styles */
        .dynamic-field {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            animation: slideDown 0.3s ease;
        }

        .dynamic-field:hover {
            border-color: #0d6efd;
            box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
        }

        .dynamic-field-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .field-title {
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .field-icon {
            width: 24px;
            height: 24px;
            background: #0d6efd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .selected-options {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .selected-tag {
                font-size: 0.8rem;
                padding: 0.2rem 0.4rem;
            }
            
            .multi-select-options {
                max-height: 150px;
            }
            
            .dynamic-field {
                padding: 0.75rem;
            }
        }

        .demo-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }

        .demo-section {
            background: white;
            border-radius: 0.5rem;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="demo-container">
        <div class="demo-section">
            <h2 class="mb-4">Multi-Select Location Dropdown Demo</h2>
            <p class="text-muted mb-4">Select multiple location options to see dynamic input fields appear below.</p>
            
            <div class="row">
                <div class="col-12 mb-3">
                    <label for="event_location" class="form-label">Location</label>
                    
                    <!-- Multi-select dropdown container -->
                    <div class="multi-select-container">
                        <div class="multi-select-dropdown" id="location-dropdown">
                            <div class="multi-select-header" onclick="toggleLocationDropdown()">
                                <div class="selected-options" id="selected-locations">
                                    <span class="placeholder">Select Location(s)</span>
                                </div>
                                <span class="dropdown-arrow">▼</span>
                            </div>
                            
                            <div class="multi-select-options" id="location-options" style="display: none;">
                                <div class="option-item" data-value="in_person">
                                    <input type="checkbox" id="loc_in_person" value="in_person">
                                    <label for="loc_in_person">In Person</label>
                                </div>
                                <div class="option-item" data-value="zoom">
                                    <input type="checkbox" id="loc_zoom" value="zoom">
                                    <label for="loc_zoom">Zoom</label>
                                </div>
                                <div class="option-item" data-value="skype">
                                    <input type="checkbox" id="loc_skype" value="skype">
                                    <label for="loc_skype">Skype</label>
                                </div>
                                <div class="option-item" data-value="meet">
                                    <input type="checkbox" id="loc_meet" value="meet">
                                    <label for="loc_meet">Google Meet</label>
                                </div>
                                <div class="option-item" data-value="others">
                                    <input type="checkbox" id="loc_others" value="others">
                                    <label for="loc_others">Others</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hidden input to store selected values -->
                    <input type="hidden" name="location" id="location_values">
                </div>
            </div>
            
            <!-- Dynamic Location Fields Container -->
            <div id="dynamic-location-fields">
                <!-- Dynamic fields will be inserted here -->
            </div>
            
            <!-- Demo Output -->
            <div class="mt-4">
                <h5>Selected Values:</h5>
                <pre id="output" class="bg-light p-3 rounded"></pre>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Multi-select location dropdown functionality
        let selectedLocations = [];

        function toggleLocationDropdown() {
            const dropdown = document.getElementById('location-dropdown');
            const options = document.getElementById('location-options');
            
            dropdown.classList.toggle('active');
            options.style.display = options.style.display === 'none' ? 'block' : 'none';
        }

        function updateSelectedLocations() {
            const selectedContainer = document.getElementById('selected-locations');
            const hiddenInput = document.getElementById('location_values');
            const output = document.getElementById('output');
            
            // Clear current display
            selectedContainer.innerHTML = '';
            
            if (selectedLocations.length === 0) {
                selectedContainer.innerHTML = '<span class="placeholder">Select Location(s)</span>';
                hiddenInput.value = '';
            } else {
                // Create tags for selected options
                selectedLocations.forEach(location => {
                    const locationText = getLocationText(location);
                    const tag = document.createElement('span');
                    tag.className = 'selected-tag';
                    tag.innerHTML = `
                        ${locationText}
                        <span class="remove-tag" onclick="removeLocation('${location}')">×</span>
                    `;
                    selectedContainer.appendChild(tag);
                });
                
                // Update hidden input
                hiddenInput.value = selectedLocations.join(',');
            }
            
            // Update dynamic fields
            updateDynamicLocationFields();
            
            // Update output
            output.textContent = JSON.stringify({
                selectedLocations: selectedLocations,
                hiddenInputValue: hiddenInput.value
            }, null, 2);
        }

        function getLocationText(value) {
            const locationMap = {
                'in_person': 'In Person',
                'zoom': 'Zoom',
                'skype': 'Skype',
                'meet': 'Google Meet',
                'others': 'Others'
            };
            return locationMap[value] || value;
        }

        function removeLocation(location) {
            selectedLocations = selectedLocations.filter(loc => loc !== location);
            document.getElementById(`loc_${location}`).checked = false;
            updateSelectedLocations();
        }

        function updateDynamicLocationFields() {
            const container = document.getElementById('dynamic-location-fields');
            container.innerHTML = '';
            
            selectedLocations.forEach(location => {
                const fieldHtml = createLocationField(location);
                container.insertAdjacentHTML('beforeend', fieldHtml);
            });
        }

        function createLocationField(location) {
            const locationText = getLocationText(location);
            let fieldContent = '';
            let icon = '';
            
            switch(location) {
                case 'in_person':
                    icon = '📍';
                    fieldContent = `
                        <div class="mb-3">
                            <label for="physical_address_${location}" class="form-label">Physical Address</label>
                            <textarea class="form-control" id="physical_address_${location}" name="location_details[${location}][address]" 
                                      rows="2" placeholder="Enter the physical address for in-person meetings"></textarea>
                        </div>
                    `;
                    break;
                case 'zoom':
                    icon = '📹';
                    fieldContent = `
                        <div class="mb-3">
                            <label for="zoom_link_${location}" class="form-label">Zoom Meeting Link</label>
                            <input type="url" class="form-control" id="zoom_link_${location}" name="location_details[${location}][link]" 
                                   placeholder="https://zoom.us/j/123456789">
                        </div>
                        <div class="mb-3">
                            <label for="zoom_id_${location}" class="form-label">Meeting ID (Optional)</label>
                            <input type="text" class="form-control" id="zoom_id_${location}" name="location_details[${location}][meeting_id]" 
                                   placeholder="123 456 789">
                        </div>
                        <div class="mb-3">
                            <label for="zoom_password_${location}" class="form-label">Password (Optional)</label>
                            <input type="text" class="form-control" id="zoom_password_${location}" name="location_details[${location}][password]" 
                                   placeholder="Meeting password">
                        </div>
                    `;
                    break;
                case 'skype':
                    icon = '💬';
                    fieldContent = `
                        <div class="mb-3">
                            <label for="skype_link_${location}" class="form-label">Skype Meeting Link</label>
                            <input type="url" class="form-control" id="skype_link_${location}" name="location_details[${location}][link]" 
                                   placeholder="https://join.skype.com/abc123">
                        </div>
                        <div class="mb-3">
                            <label for="skype_id_${location}" class="form-label">Skype ID (Optional)</label>
                            <input type="text" class="form-control" id="skype_id_${location}" name="location_details[${location}][skype_id]" 
                                   placeholder="your.skype.id">
                        </div>
                    `;
                    break;
                case 'meet':
                    icon = '🎥';
                    fieldContent = `
                        <div class="mb-3">
                            <label for="meet_link_${location}" class="form-label">Google Meet Link</label>
                            <input type="url" class="form-control" id="meet_link_${location}" name="location_details[${location}][link]" 
                                   placeholder="https://meet.google.com/abc-defg-hij">
                        </div>
                        <div class="mb-3">
                            <label for="meet_phone_${location}" class="form-label">Phone Number (Optional)</label>
                            <input type="tel" class="form-control" id="meet_phone_${location}" name="location_details[${location}][phone]" 
                                   placeholder="******-567-8900">
                        </div>
                    `;
                    break;
                case 'others':
                    icon = '🌐';
                    fieldContent = `
                        <div class="mb-3">
                            <label for="other_platform_${location}" class="form-label">Platform Name</label>
                            <input type="text" class="form-control" id="other_platform_${location}" name="location_details[${location}][platform]" 
                                   placeholder="e.g., Microsoft Teams, WebEx, etc.">
                        </div>
                        <div class="mb-3">
                            <label for="other_link_${location}" class="form-label">Meeting Link</label>
                            <input type="url" class="form-control" id="other_link_${location}" name="location_details[${location}][link]" 
                                   placeholder="https://example.com/meeting">
                        </div>
                        <div class="mb-3">
                            <label for="other_details_${location}" class="form-label">Additional Details</label>
                            <textarea class="form-control" id="other_details_${location}" name="location_details[${location}][details]" 
                                      rows="2" placeholder="Any additional meeting details or instructions"></textarea>
                        </div>
                    `;
                    break;
            }
            
            return `
                <div class="dynamic-field" data-location="${location}">
                    <div class="dynamic-field-header">
                        <div class="field-title">
                            <div class="field-icon">
                                ${icon}
                            </div>
                            ${locationText} Details
                        </div>
                    </div>
                    ${fieldContent}
                </div>
            `;
        }

        // Initialize functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle checkbox changes
            document.querySelectorAll('#location-options input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const value = this.value;
                    const isChecked = this.checked;
                    
                    if (isChecked) {
                        if (!selectedLocations.includes(value)) {
                            selectedLocations.push(value);
                        }
                    } else {
                        selectedLocations = selectedLocations.filter(loc => loc !== value);
                    }
                    
                    updateSelectedLocations();
                });
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.multi-select-container')) {
                    document.getElementById('location-dropdown').classList.remove('active');
                    document.getElementById('location-options').style.display = 'none';
                }
            });
            
            // Prevent dropdown from closing when clicking inside options
            document.getElementById('location-options').addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    </script>
</body>
</html>
