[2025-07-23 17:18:49] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events/calendar-data","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:33:33] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events/calendar-data","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:33:39] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events/calendar-data","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:33:42] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:33:44] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:37:38] local.INFO: Request {"title":"ok","start_date":"2025-07-23T23:09","end_date":"2025-07-31T23:06","duration":"60","booking_per_slot":"1","minimum_notice":"30","description":"ok","location":"in_person,zoom,skype,meet","location_details":{"in_person":{"address":"Siliguri"},"zoom":{"link":"https://meet.google.com/mqg-efoj-kug","meeting_id":null,"password":null},"skype":{"link":"https://meet.google.com/mqg-efoj-kug","skype_id":null},"meet":{"link":"https://meet.google.com/mqg-efoj-kug","phone":null}},"require_name":"false","require_email":"false","require_phone":"false","custom_field":null,"availability":{"monday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"tuesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"wednesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"thursday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"friday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"saturday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"sunday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]}},"_token":"8TKLy6mAKKq3FjUyUoaGVX8Ri40nlsfy44BqOwTH"} 
[2025-07-23 17:37:40] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:38:04] local.INFO: Request {"title":"ok","start_date":"2025-07-23T23:09","end_date":"2025-07-31T23:06","duration":"60","booking_per_slot":"1","minimum_notice":"30","description":"ok","location":"in_person,zoom,skype,meet","location_details":{"in_person":{"address":"Siliguri west bengal"},"zoom":{"link":"https://meet.google.com/mqg-efoj-kug","meeting_id":null,"password":null},"skype":{"link":"https://meet.google.com/mqg-efoj-kug","skype_id":null},"meet":{"link":"https://meet.google.com/mqg-efoj-kug","phone":null}},"require_name":"false","require_email":"false","require_phone":"false","custom_field":null,"availability":{"monday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"tuesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"wednesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"thursday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"friday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"saturday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"sunday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]}},"_token":"8TKLy6mAKKq3FjUyUoaGVX8Ri40nlsfy44BqOwTH"} 
[2025-07-23 17:38:04] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:38:32] local.INFO: Request {"title":"ok","start_date":"2025-07-23T23:09","end_date":"2025-07-31T23:06","duration":"60","booking_per_slot":"1","minimum_notice":"30","description":"ok","location":"in_person,zoom,skype,meet","location_details":{"in_person":{"address":"Siliguri west bengal"},"zoom":{"link":"https://meet.google.com/mqg-efoj-kug","meeting_id":null,"password":null},"skype":{"link":"https://meet.google.com/mqg-efoj-kug","skype_id":null},"meet":{"link":"https://meet.google.com/mqg-efoj-kug","phone":null}},"require_name":"false","require_email":"false","require_phone":"false","custom_field":null,"custom_fields":[{"type":"full_name","label":"Full Name"}],"availability":{"monday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"tuesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"wednesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"thursday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"friday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"saturday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"sunday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]}},"date_override":[{"datetime":"2025-07-23T14:08"}],"_token":"8TKLy6mAKKq3FjUyUoaGVX8Ri40nlsfy44BqOwTH"} 
[2025-07-23 17:38:32] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:43:06] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events/calendar-data","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:47:10] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events/calendar-data","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:47:15] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events/calendar-data","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:47:17] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/getContacts","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:47:19] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/chats/favorites","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:51:22] local.INFO: Request {"title":"Bot Digital","start_date":"2025-07-23T13:19","end_date":"2025-07-31T23:17","duration":"60","booking_per_slot":"1","minimum_notice":"30","description":"Ok, this is best.","location":"in_person,zoom,meet","location_details":{"in_person":{"address":"siliguri west bengal"},"zoom":{"link":"https://meet.google.com/jdf-weft-jfv","meeting_id":null,"password":null},"meet":{"link":"https://meet.google.com/jdf-weft-jfv","phone":null}},"require_name":"false","require_email":"false","require_phone":"false","custom_field":null,"custom_fields":[{"type":"full_name","label":"Full Name"}],"availability":{"monday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"tuesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"wednesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"thursday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"friday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"saturday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"sunday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]}},"date_override":[{"datetime":"2025-07-23T23:25"}],"_token":"8TKLy6mAKKq3FjUyUoaGVX8Ri40nlsfy44BqOwTH"} 
[2025-07-23 17:51:22] local.INFO: Incoming request data: {"title":"Bot Digital","start_date":"2025-07-23T13:19","end_date":"2025-07-31T23:17","duration":"60","booking_per_slot":"1","minimum_notice":"30","description":"Ok, this is best.","location":"in_person,zoom,meet","location_details":{"in_person":{"address":"siliguri west bengal"},"zoom":{"link":"https://meet.google.com/jdf-weft-jfv","meeting_id":null,"password":null},"meet":{"link":"https://meet.google.com/jdf-weft-jfv","phone":null}},"require_name":"false","require_email":"false","require_phone":"false","custom_field":null,"custom_fields":[{"type":"full_name","label":"Full Name"}],"availability":{"monday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"tuesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"wednesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"thursday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"friday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"saturday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"sunday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]}},"date_override":[{"datetime":"2025-07-23T23:25"}],"_token":"8TKLy6mAKKq3FjUyUoaGVX8Ri40nlsfy44BqOwTH"} 
[2025-07-23 17:51:22] local.INFO: Weekly availability data: {"monday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"tuesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"wednesday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"thursday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"friday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"saturday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]},"sunday":{"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]}} 
[2025-07-23 17:51:22] local.INFO: Processing day: monday {"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]} 
[2025-07-23 17:51:22] local.INFO: Creating slot for monday: {"start":"09:00","end":"17:00"} 
[2025-07-23 17:51:22] local.INFO: Processing day: tuesday {"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]} 
[2025-07-23 17:51:22] local.INFO: Creating slot for tuesday: {"start":"09:00","end":"17:00"} 
[2025-07-23 17:51:22] local.INFO: Processing day: wednesday {"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]} 
[2025-07-23 17:51:22] local.INFO: Creating slot for wednesday: {"start":"09:00","end":"17:00"} 
[2025-07-23 17:51:22] local.INFO: Processing day: thursday {"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]} 
[2025-07-23 17:51:22] local.INFO: Creating slot for thursday: {"start":"09:00","end":"17:00"} 
[2025-07-23 17:51:22] local.INFO: Processing day: friday {"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]} 
[2025-07-23 17:51:22] local.INFO: Creating slot for friday: {"start":"09:00","end":"17:00"} 
[2025-07-23 17:51:22] local.INFO: Processing day: saturday {"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]} 
[2025-07-23 17:51:22] local.INFO: Creating slot for saturday: {"start":"09:00","end":"17:00"} 
[2025-07-23 17:51:22] local.INFO: Processing day: sunday {"enabled":"true","slots":[{"start":"09:00","end":"17:00"}]} 
[2025-07-23 17:51:22] local.INFO: Creating slot for sunday: {"start":"09:00","end":"17:00"} 
[2025-07-23 17:51:22] local.INFO: Starting slot generation for event ID: 4  
[2025-07-23 17:51:22] local.INFO: Event date range: 2025-07-23 to 2025-07-31  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-23 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-23 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-23 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-23 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-23 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-23 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-23 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-23 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-23 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-24 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-24 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-24 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-24 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-24 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-24 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-24 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-24 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-24 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-25 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-25 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-25 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-25 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-25 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-25 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-25 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-25 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-25 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-26 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-26 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-26 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-26 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-26 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-26 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-26 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-26 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-26 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-27 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-27 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-27 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-27 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-27 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-27 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-27 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-27 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-27 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-28 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-28 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-28 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-28 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-28 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-28 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-28 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-28 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-28 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-29 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-29 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-29 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-29 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-29 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-29 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-29 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-29 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-29 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-30 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-30 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-30 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-30 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-30 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-30 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-30 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-30 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-30 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generating slots for 2025-07-31 from 09:00 to 17:00 with 60min duration  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-31 09:00-10:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-31 10:00-11:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-31 11:00-12:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-31 12:00-13:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-31 13:00-14:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-31 14:00-15:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-31 15:00-16:00  
[2025-07-23 17:51:22] local.DEBUG: Created slot: 2025-07-31 16:00-17:00  
[2025-07-23 17:51:22] local.INFO: Generated 72 slots for event ID: 4  
[2025-07-23 17:51:22] local.INFO: Generated 72 appointment slots for event ID: 4  
[2025-07-23 17:51:30] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events","has_pricing_plan":true,"has_module_permissions":true} 
[2025-07-23 17:51:31] local.INFO: Permissions refreshed after POST request {"user_id":84,"user_type":"company","request_url":"http://127.0.0.1:8000/calendar-events/calendar-data","has_pricing_plan":true,"has_module_permissions":true} 
