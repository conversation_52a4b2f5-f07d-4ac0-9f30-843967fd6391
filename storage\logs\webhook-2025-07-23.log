[2025-07-23 12:54:06] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-23T12:54:06.619091Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-23 12:54:16] local.ERROR: Webhook failed for OMX FLOW: HTTP Error: 404 {"timestamp":"2025-07-23T12:54:16.405817Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":404,"response_time_ms":9759.0,"user_id":84,"entity_id":3,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-23T12:54:06.646877Z","data":{"id":3,"title":"Smart Bot Builder","start_date":"2025-07-25 18:24:00","end_date":"2025-07-31 18:25:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"an unexpected difference, esp. in two amounts or two sets of facts or conditions, which suggests that something is wrong and has to be explained.","location":"in_person","meet_link":null,"physical_address":"Siliguri Westbengal","custom_fields":[{"type":"whatsapp_number","label":"Whatsapp Number"},{"type":"postal_code","label":"Postal Code"},{"type":"full_name","label":"Full Name"}],"date_override":null,"created_by":84,"slots_created":56,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"HTTP Error: 404","response_body":"<!DOCTYPE html>

<html lang=\"en\" dir=\"\">



<head>

    <meta charset=\"utf-8\">

    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">

    <meta name=\"description\" content=\"\">

    <meta name=\"author\" content=\"\">

    <!-- Primary Meta Tags -->

    <title>Not Found</title>

    <meta name=\"title\" content=\"Not Found\">

    <meta name=\"description\" content=\"Not Found\">



    <!-- Open Graph / Facebook -->

    <meta property=\"og:type\" content=\"website\">

    <meta property=\"og:url\" content=\"http://127.0.0.1:2000\">

    <meta property=\"og:title\" content=\"Not Found\">

    <meta property=\"og:description\" content=\"Not Found\">

    <meta property=\"og:image\" content=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\">



    <!-- Twitter -->

    <meta property=\"twitter:card\" content=\"summary_large_image\">

    <meta property=\"twitter:url\" content=\"http://127.0.0.1:2000\">

    <meta property=\"twitter:title\" content=\"Not Found\">

    <meta property=\"twitter:description\" content=\"Not Found\">

    <meta property=\"twitter:image\" content=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\">



    <link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/static-assets/packages/fontawesome/css/all.css?sign=af83425d4a008e11110634e9aed99e8f91424a99\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/dist/css/vendorlibs.css?sign=1ab0c6122d9cf16bb1ca799cf4b9ce92ae0a70e3\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/argon/css/argon.min.css?sign=3642592f8bb0d4482afd36bb547122840ea143db\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/dist/css/app.css?sign=b32b82c9a54df294214ac58e5015c9469cbd88a8\"/>



    <link rel=\"shortcut icon\" href=\"http://127.0.0.1:2000/media-storage/favicon/67f8a1eb374a7---67dbcd0eebf6a-omxiconn.png\" type=\"image/x-icon\">

    <link rel=\"icon\" href=\"http://127.0.0.1:2000/media-storage/favicon/67f8a1eb374a7---67dbcd0eebf6a-omxiconn.png\" type=\"image/x-icon\">

</head>



<body id=\"page-top\" class=\"lw-gradient-bg\">



    <!-- Page Wrapper -->

    <!-- Begin Page Content -->

    <div class=\"lw-page-content lw-other-page-content\">

        <section class=\"section \">

            <div class=\"container text-center\">

                <div class=\"row\">

                    <div class=\"col-12 my-1\">

                        <img class=\"lw-logo-img mt-5\" src=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\"

                            alt=\"OMX FLOW PANEL\">

                    </div>

                </div>

                <div class=\"row\">

                    <div class=\"col-8 offset-2  mb-5 lw-error-page-block\">

                        <i class=\"fas fa-exclamation-triangle fa-10x text-danger\"></i>

                        <h1 class=\"fa-8x\">404</h1>

                            <h2 class=\"text-muted\"> Not Found</h2>

                        <p class=\"my-5 fa-2x\">The page you are requesting does not exists.</p>

                      <div class=\"mb-6\">

                        <a href=\"http://127.0.0.1:2000\" class=\"btn btn-primary btn-lg mt-5\">Back to Home</a>

                      </div>

                    </div>

                </div>

            </div>

        </section>

    </div>

</body>



</html>"} 
[2025-07-23 12:54:16] local.ERROR: Webhook failed for WhatsApp Flow: HTTP Error: 404 {"timestamp":"2025-07-23T12:54:16.827830Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":404,"response_time_ms":420.0,"user_id":84,"entity_id":3,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-23T12:54:16.407310Z","data":{"id":3,"title":"Smart Bot Builder","start_date":"2025-07-25 18:24:00","end_date":"2025-07-31 18:25:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"an unexpected difference, esp. in two amounts or two sets of facts or conditions, which suggests that something is wrong and has to be explained.","location":"in_person","meet_link":null,"physical_address":"Siliguri Westbengal","custom_fields":[{"type":"whatsapp_number","label":"Whatsapp Number"},{"type":"postal_code","label":"Postal Code"},{"type":"full_name","label":"Full Name"}],"date_override":null,"created_by":84,"slots_created":56,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"HTTP Error: 404","response_body":"<!DOCTYPE html>

<html lang=\"en\" dir=\"\">



<head>

    <meta charset=\"utf-8\">

    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">

    <meta name=\"description\" content=\"\">

    <meta name=\"author\" content=\"\">

    <!-- Primary Meta Tags -->

    <title>Not Found</title>

    <meta name=\"title\" content=\"Not Found\">

    <meta name=\"description\" content=\"Not Found\">



    <!-- Open Graph / Facebook -->

    <meta property=\"og:type\" content=\"website\">

    <meta property=\"og:url\" content=\"http://127.0.0.1:2000\">

    <meta property=\"og:title\" content=\"Not Found\">

    <meta property=\"og:description\" content=\"Not Found\">

    <meta property=\"og:image\" content=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\">



    <!-- Twitter -->

    <meta property=\"twitter:card\" content=\"summary_large_image\">

    <meta property=\"twitter:url\" content=\"http://127.0.0.1:2000\">

    <meta property=\"twitter:title\" content=\"Not Found\">

    <meta property=\"twitter:description\" content=\"Not Found\">

    <meta property=\"twitter:image\" content=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\">



    <link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/static-assets/packages/fontawesome/css/all.css?sign=af83425d4a008e11110634e9aed99e8f91424a99\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/dist/css/vendorlibs.css?sign=1ab0c6122d9cf16bb1ca799cf4b9ce92ae0a70e3\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/argon/css/argon.min.css?sign=3642592f8bb0d4482afd36bb547122840ea143db\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/dist/css/app.css?sign=b32b82c9a54df294214ac58e5015c9469cbd88a8\"/>



    <link rel=\"shortcut icon\" href=\"http://127.0.0.1:2000/media-storage/favicon/67f8a1eb374a7---67dbcd0eebf6a-omxiconn.png\" type=\"image/x-icon\">

    <link rel=\"icon\" href=\"http://127.0.0.1:2000/media-storage/favicon/67f8a1eb374a7---67dbcd0eebf6a-omxiconn.png\" type=\"image/x-icon\">

</head>



<body id=\"page-top\" class=\"lw-gradient-bg\">



    <!-- Page Wrapper -->

    <!-- Begin Page Content -->

    <div class=\"lw-page-content lw-other-page-content\">

        <section class=\"section \">

            <div class=\"container text-center\">

                <div class=\"row\">

                    <div class=\"col-12 my-1\">

                        <img class=\"lw-logo-img mt-5\" src=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\"

                            alt=\"OMX FLOW PANEL\">

                    </div>

                </div>

                <div class=\"row\">

                    <div class=\"col-8 offset-2  mb-5 lw-error-page-block\">

                        <i class=\"fas fa-exclamation-triangle fa-10x text-danger\"></i>

                        <h1 class=\"fa-8x\">404</h1>

                            <h2 class=\"text-muted\"> Not Found</h2>

                        <p class=\"my-5 fa-2x\">The page you are requesting does not exists.</p>

                      <div class=\"mb-6\">

                        <a href=\"http://127.0.0.1:2000\" class=\"btn btn-primary btn-lg mt-5\">Back to Home</a>

                      </div>

                    </div>

                </div>

            </div>

        </section>

    </div>

</body>



</html>"} 
[2025-07-23 12:54:16] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-23T12:54:16.828649Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"status_code":404,"error":"<!DOCTYPE html>

<html lang=\"en\" dir=\"\">



<head>

    <meta charset=\"utf-8\">

    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">

    <meta name=\"description\" content=\"\">

    <meta name=\"author\" content=\"\">

    <!-- Primary Meta Tags -->

    <title>Not Found</title>

    <meta name=\"title\" content=\"Not Found\">

    <meta name=\"description\" content=\"Not Found\">



    <!-- Open Graph / Facebook -->

    <meta property=\"og:type\" content=\"website\">

    <meta property=\"og:url\" content=\"http://127.0.0.1:2000\">

    <meta property=\"og:title\" content=\"Not Found\">

    <meta property=\"og:description\" content=\"Not Found\">

    <meta property=\"og:image\" content=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\">



    <!-- Twitter -->

    <meta property=\"twitter:card\" content=\"summary_large_image\">

    <meta property=\"twitter:url\" content=\"http://127.0.0.1:2000\">

    <meta property=\"twitter:title\" content=\"Not Found\">

    <meta property=\"twitter:description\" content=\"Not Found\">

    <meta property=\"twitter:image\" content=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\">



    <link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/static-assets/packages/fontawesome/css/all.css?sign=af83425d4a008e11110634e9aed99e8f91424a99\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/dist/css/vendorlibs.css?sign=1ab0c6122d9cf16bb1ca799cf4b9ce92ae0a70e3\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/argon/css/argon.min.css?sign=3642592f8bb0d4482afd36bb547122840ea143db\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/dist/css/app.css?sign=b32b82c9a54df294214ac58e5015c9469cbd88a8\"/>



    <link rel=\"shortcut icon\" href=\"http://127.0.0.1:2000/media-storage/favicon/67f8a1eb374a7---67dbcd0eebf6a-omxiconn.png\" type=\"image/x-icon\">

    <link rel=\"icon\" href=\"http://127.0.0.1:2000/media-storage/favicon/67f8a1eb374a7---67dbcd0eebf6a-omxiconn.png\" type=\"image/x-icon\">

</head>



<body id=\"page-top\" class=\"lw-gradient-bg\">



    <!-- Page Wrapper -->

    <!-- Begin Page Content -->

    <div class=\"lw-page-content lw-other-page-content\">

        <section class=\"section \">

            <div class=\"container text-center\">

                <div class=\"row\">

                    <div class=\"col-12 my-1\">

                        <img class=\"lw-logo-img mt-5\" src=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\"

                            alt=\"OMX FLOW PANEL\">

                    </div>

                </div>

                <div class=\"row\">

                    <div class=\"col-8 offset-2  mb-5 lw-error-page-block\">

                        <i class=\"fas fa-exclamation-triangle fa-10x text-danger\"></i>

                        <h1 class=\"fa-8x\">404</h1>

                            <h2 class=\"text-muted\"> Not Found</h2>

                        <p class=\"my-5 fa-2x\">The page you are requesting does not exists.</p>

                      <div class=\"mb-6\">

                        <a href=\"http://127.0.0.1:2000\" class=\"btn btn-primary btn-lg mt-5\">Back to Home</a>

                      </div>

                    </div>

                </div>

            </div>

        </section>

    </div>

</body>



</html>","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"status_code":404,"error":"<!DOCTYPE html>

<html lang=\"en\" dir=\"\">



<head>

    <meta charset=\"utf-8\">

    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">

    <meta name=\"description\" content=\"\">

    <meta name=\"author\" content=\"\">

    <!-- Primary Meta Tags -->

    <title>Not Found</title>

    <meta name=\"title\" content=\"Not Found\">

    <meta name=\"description\" content=\"Not Found\">



    <!-- Open Graph / Facebook -->

    <meta property=\"og:type\" content=\"website\">

    <meta property=\"og:url\" content=\"http://127.0.0.1:2000\">

    <meta property=\"og:title\" content=\"Not Found\">

    <meta property=\"og:description\" content=\"Not Found\">

    <meta property=\"og:image\" content=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\">



    <!-- Twitter -->

    <meta property=\"twitter:card\" content=\"summary_large_image\">

    <meta property=\"twitter:url\" content=\"http://127.0.0.1:2000\">

    <meta property=\"twitter:title\" content=\"Not Found\">

    <meta property=\"twitter:description\" content=\"Not Found\">

    <meta property=\"twitter:image\" content=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\">



    <link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/static-assets/packages/fontawesome/css/all.css?sign=af83425d4a008e11110634e9aed99e8f91424a99\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/dist/css/vendorlibs.css?sign=1ab0c6122d9cf16bb1ca799cf4b9ce92ae0a70e3\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/argon/css/argon.min.css?sign=3642592f8bb0d4482afd36bb547122840ea143db\"/>

<link rel=\"stylesheet\" type=\"text/css\" href=\"http://127.0.0.1:2000/dist/css/app.css?sign=b32b82c9a54df294214ac58e5015c9469cbd88a8\"/>



    <link rel=\"shortcut icon\" href=\"http://127.0.0.1:2000/media-storage/favicon/67f8a1eb374a7---67dbcd0eebf6a-omxiconn.png\" type=\"image/x-icon\">

    <link rel=\"icon\" href=\"http://127.0.0.1:2000/media-storage/favicon/67f8a1eb374a7---67dbcd0eebf6a-omxiconn.png\" type=\"image/x-icon\">

</head>



<body id=\"page-top\" class=\"lw-gradient-bg\">



    <!-- Page Wrapper -->

    <!-- Begin Page Content -->

    <div class=\"lw-page-content lw-other-page-content\">

        <section class=\"section \">

            <div class=\"container text-center\">

                <div class=\"row\">

                    <div class=\"col-12 my-1\">

                        <img class=\"lw-logo-img mt-5\" src=\"http://127.0.0.1:2000/media-storage/logo/67f8a1d186ec9---67e134efc38f0-677fb298276c0-untitled-design-3.png\"

                            alt=\"OMX FLOW PANEL\">

                    </div>

                </div>

                <div class=\"row\">

                    <div class=\"col-8 offset-2  mb-5 lw-error-page-block\">

                        <i class=\"fas fa-exclamation-triangle fa-10x text-danger\"></i>

                        <h1 class=\"fa-8x\">404</h1>

                            <h2 class=\"text-muted\"> Not Found</h2>

                        <p class=\"my-5 fa-2x\">The page you are requesting does not exists.</p>

                      <div class=\"mb-6\">

                        <a href=\"http://127.0.0.1:2000\" class=\"btn btn-primary btn-lg mt-5\">Back to Home</a>

                      </div>

                    </div>

                </div>

            </div>

        </section>

    </div>

</body>



</html>","integration":"WhatsApp Flow"}}} 
[2025-07-23 17:51:23] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-23T17:51:23.080909Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-23 17:51:28] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-23T17:51:28.084608Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":4919.0,"user_id":84,"entity_id":4,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-23T17:51:23.165771Z","data":{"id":4,"title":"Bot Digital","start_date":"2025-07-23 13:19:00","end_date":"2025-07-31 23:17:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"Ok, this is best.","location":"in_person,zoom,meet","meet_link":null,"physical_address":null,"custom_fields":[{"type":"full_name","label":"Full Name"}],"date_override":"[{\"datetime\":\"2025-07-23T23:25\"}]","created_by":84,"slots_created":72,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-23 17:51:30] local.ERROR: Webhook failed for WhatsApp Flow: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-23T17:51:30.122616Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"WhatsApp Flow","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2036.0,"user_id":84,"entity_id":4,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-23T17:51:28.086356Z","data":{"id":4,"title":"Bot Digital","start_date":"2025-07-23 13:19:00","end_date":"2025-07-31 23:17:00","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"Ok, this is best.","location":"in_person,zoom,meet","meet_link":null,"physical_address":null,"custom_fields":[{"type":"full_name","label":"Full Name"}],"date_override":"[{\"datetime\":\"2025-07-23T23:25\"}]","created_by":84,"slots_created":72,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-23 17:51:30] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 2 {"timestamp":"2025-07-23T17:51:30.124127Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":2,"successful_modules":0,"failed_modules":2,"modules":["OMX FLOW","WhatsApp Flow"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2048 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"},"WhatsApp Flow":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"WhatsApp Flow"}}} 
